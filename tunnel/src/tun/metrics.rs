//! # TUN 性能监控模块
//!
//! 本模块提供 TUN 设备的性能监控和统计功能，
//! 包括连接统计、流量统计、错误统计等。

use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};

use log::{debug, info, warn};
use tokio::sync::RwLock;
use tokio::time::interval;

/// TUN 性能指标
#[derive(Debug, Default)]
pub struct TunMetrics {
    /// TCP 连接统计
    pub tcp_stats: TcpMetrics,
    /// UDP 数据包统计
    pub udp_stats: UdpMetrics,
    /// 网络栈统计
    pub netstack_stats: NetStackMetrics,
    /// 错误统计
    pub error_stats: ErrorMetrics,
    /// 性能统计
    pub performance_stats: PerformanceMetrics,
}

/// TCP 连接统计
#[derive(Debug, Default)]
pub struct TcpMetrics {
    /// 总连接数
    pub total_connections: AtomicU64,
    /// 当前活跃连接数
    pub active_connections: AtomicU64,
    /// 成功连接数
    pub successful_connections: AtomicU64,
    /// 失败连接数
    pub failed_connections: AtomicU64,
    /// 被封锁的连接数
    pub blocked_connections: AtomicU64,
    /// 平均连接持续时间（毫秒）
    pub avg_connection_duration_ms: AtomicU64,
}

/// UDP 数据包统计
#[derive(Debug, Default)]
pub struct UdpMetrics {
    /// 总数据包数
    pub total_packets: AtomicU64,
    /// 处理的数据包数
    pub processed_packets: AtomicU64,
    /// 被封锁的数据包数
    pub blocked_packets: AtomicU64,
    /// 失败的数据包数
    pub failed_packets: AtomicU64,
    /// 总字节数
    pub total_bytes: AtomicU64,
    /// 平均数据包大小（字节）
    pub avg_packet_size: AtomicU64,
}

/// 网络栈统计
#[derive(Debug, Default)]
pub struct NetStackMetrics {
    /// 发送到客户端的数据包数
    pub packets_to_client: AtomicU64,
    /// 从 TUN 设备接收的数据包数
    pub packets_from_tun: AtomicU64,
    /// 发送到客户端的字节数
    pub bytes_to_client: AtomicU64,
    /// 从 TUN 设备接收的字节数
    pub bytes_from_tun: AtomicU64,
    /// 网络栈错误数
    pub netstack_errors: AtomicU64,
}

/// 错误统计
#[derive(Debug, Default)]
pub struct ErrorMetrics {
    /// 网络栈初始化错误
    pub netstack_init_errors: AtomicU64,
    /// 通道错误
    pub channel_errors: AtomicU64,
    /// TCP 连接错误
    pub tcp_connection_errors: AtomicU64,
    /// UDP 传输错误
    pub udp_transmission_errors: AtomicU64,
    /// 隧道连接错误
    pub tunnel_connection_errors: AtomicU64,
    /// DNS 解析错误
    pub dns_resolution_errors: AtomicU64,
    /// 配置错误
    pub configuration_errors: AtomicU64,
    /// I/O 错误
    pub io_errors: AtomicU64,
}

/// 性能统计
#[derive(Debug, Default)]
pub struct PerformanceMetrics {
    /// 启动时间
    pub start_time: RwLock<Option<Instant>>,
    /// 运行时间（秒）
    pub uptime_seconds: AtomicU64,
    /// CPU 使用率（百分比）
    pub cpu_usage_percent: AtomicU64,
    /// 内存使用量（字节）
    pub memory_usage_bytes: AtomicU64,
    /// 平均响应时间（微秒）
    pub avg_response_time_us: AtomicU64,
}

impl TunMetrics {
    /// 创建新的指标实例
    pub fn new() -> Arc<Self> {
        Arc::new(Self::default())
    }

    /// 启动指标收集
    pub async fn start_collection(metrics: Arc<Self>) {
        // 设置启动时间
        *metrics.performance_stats.start_time.write().await = Some(Instant::now());

        // 启动定期统计任务
        let metrics_clone = metrics.clone();
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(60)); // 每分钟统计一次
            
            loop {
                interval.tick().await;
                Self::log_periodic_stats(&metrics_clone).await;
            }
        });

        // 启动性能监控任务
        let metrics_clone = metrics.clone();
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(10)); // 每10秒更新一次性能指标
            
            loop {
                interval.tick().await;
                Self::update_performance_stats(&metrics_clone).await;
            }
        });
    }

    /// 记录定期统计信息
    async fn log_periodic_stats(metrics: &Arc<Self>) {
        let tcp_total = metrics.tcp_stats.total_connections.load(Ordering::Relaxed);
        let tcp_active = metrics.tcp_stats.active_connections.load(Ordering::Relaxed);
        let tcp_failed = metrics.tcp_stats.failed_connections.load(Ordering::Relaxed);
        let tcp_blocked = metrics.tcp_stats.blocked_connections.load(Ordering::Relaxed);

        let udp_total = metrics.udp_stats.total_packets.load(Ordering::Relaxed);
        let udp_processed = metrics.udp_stats.processed_packets.load(Ordering::Relaxed);
        let udp_blocked = metrics.udp_stats.blocked_packets.load(Ordering::Relaxed);
        let udp_bytes = metrics.udp_stats.total_bytes.load(Ordering::Relaxed);

        let uptime = metrics.performance_stats.uptime_seconds.load(Ordering::Relaxed);

        info!(
            "TUN 统计 - 运行时间: {}s, TCP: 总计={}, 活跃={}, 失败={}, 封锁={}, UDP: 总计={}, 处理={}, 封锁={}, 字节={}",
            uptime, tcp_total, tcp_active, tcp_failed, tcp_blocked,
            udp_total, udp_processed, udp_blocked, udp_bytes
        );

        // 检查异常情况
        if tcp_failed > tcp_total / 10 {
            warn!("TCP 连接失败率过高: {}/{}", tcp_failed, tcp_total);
        }

        if udp_blocked > udp_total / 5 {
            warn!("UDP 数据包封锁率过高: {}/{}", udp_blocked, udp_total);
        }
    }

    /// 更新性能统计
    async fn update_performance_stats(metrics: &Arc<Self>) {
        if let Some(start_time) = *metrics.performance_stats.start_time.read().await {
            let uptime = start_time.elapsed().as_secs();
            metrics.performance_stats.uptime_seconds.store(uptime, Ordering::Relaxed);
        }

        // 这里可以添加更多的性能指标收集逻辑
        // 例如：CPU 使用率、内存使用量等
        // 由于这些指标需要系统级别的访问，这里只是示例
    }

    /// 记录 TCP 连接开始
    pub fn record_tcp_connection_start(&self) {
        self.tcp_stats.total_connections.fetch_add(1, Ordering::Relaxed);
        self.tcp_stats.active_connections.fetch_add(1, Ordering::Relaxed);
    }

    /// 记录 TCP 连接结束
    pub fn record_tcp_connection_end(&self, success: bool, duration: Duration) {
        self.tcp_stats.active_connections.fetch_sub(1, Ordering::Relaxed);
        
        if success {
            self.tcp_stats.successful_connections.fetch_add(1, Ordering::Relaxed);
        } else {
            self.tcp_stats.failed_connections.fetch_add(1, Ordering::Relaxed);
        }

        // 更新平均连接持续时间
        let duration_ms = duration.as_millis() as u64;
        self.tcp_stats.avg_connection_duration_ms.store(duration_ms, Ordering::Relaxed);
    }

    /// 记录 TCP 连接被封锁
    pub fn record_tcp_connection_blocked(&self) {
        self.tcp_stats.blocked_connections.fetch_add(1, Ordering::Relaxed);
    }

    /// 记录 UDP 数据包处理
    pub fn record_udp_packet(&self, size: usize, processed: bool, blocked: bool) {
        self.udp_stats.total_packets.fetch_add(1, Ordering::Relaxed);
        self.udp_stats.total_bytes.fetch_add(size as u64, Ordering::Relaxed);

        if processed {
            self.udp_stats.processed_packets.fetch_add(1, Ordering::Relaxed);
        } else {
            self.udp_stats.failed_packets.fetch_add(1, Ordering::Relaxed);
        }

        if blocked {
            self.udp_stats.blocked_packets.fetch_add(1, Ordering::Relaxed);
        }

        // 更新平均数据包大小
        let total_packets = self.udp_stats.total_packets.load(Ordering::Relaxed);
        let total_bytes = self.udp_stats.total_bytes.load(Ordering::Relaxed);
        if total_packets > 0 {
            let avg_size = total_bytes / total_packets;
            self.udp_stats.avg_packet_size.store(avg_size, Ordering::Relaxed);
        }
    }

    /// 记录网络栈数据传输
    pub fn record_netstack_transfer(&self, to_client_bytes: usize, from_tun_bytes: usize) {
        if to_client_bytes > 0 {
            self.netstack_stats.packets_to_client.fetch_add(1, Ordering::Relaxed);
            self.netstack_stats.bytes_to_client.fetch_add(to_client_bytes as u64, Ordering::Relaxed);
        }

        if from_tun_bytes > 0 {
            self.netstack_stats.packets_from_tun.fetch_add(1, Ordering::Relaxed);
            self.netstack_stats.bytes_from_tun.fetch_add(from_tun_bytes as u64, Ordering::Relaxed);
        }
    }

    /// 记录错误
    pub fn record_error(&self, error_type: &str) {
        match error_type {
            "netstack_init" => self.error_stats.netstack_init_errors.fetch_add(1, Ordering::Relaxed),
            "channel" => self.error_stats.channel_errors.fetch_add(1, Ordering::Relaxed),
            "tcp_connection" => self.error_stats.tcp_connection_errors.fetch_add(1, Ordering::Relaxed),
            "udp_transmission" => self.error_stats.udp_transmission_errors.fetch_add(1, Ordering::Relaxed),
            "tunnel_connection" => self.error_stats.tunnel_connection_errors.fetch_add(1, Ordering::Relaxed),
            "dns_resolution" => self.error_stats.dns_resolution_errors.fetch_add(1, Ordering::Relaxed),
            "configuration" => self.error_stats.configuration_errors.fetch_add(1, Ordering::Relaxed),
            "io" => self.error_stats.io_errors.fetch_add(1, Ordering::Relaxed),
            _ => {
                debug!("未知错误类型: {}", error_type);
                0
            }
        };
    }

    /// 获取统计摘要
    pub fn get_summary(&self) -> String {
        let tcp_total = self.tcp_stats.total_connections.load(Ordering::Relaxed);
        let tcp_active = self.tcp_stats.active_connections.load(Ordering::Relaxed);
        let udp_total = self.udp_stats.total_packets.load(Ordering::Relaxed);
        let udp_bytes = self.udp_stats.total_bytes.load(Ordering::Relaxed);
        let uptime = self.performance_stats.uptime_seconds.load(Ordering::Relaxed);

        format!(
            "TUN 统计摘要 - 运行时间: {}s, TCP连接: {}/{}, UDP数据包: {}, 流量: {}字节",
            uptime, tcp_active, tcp_total, udp_total, udp_bytes
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_metrics_creation() {
        let metrics = TunMetrics::new();
        assert_eq!(metrics.tcp_stats.total_connections.load(Ordering::Relaxed), 0);
        assert_eq!(metrics.udp_stats.total_packets.load(Ordering::Relaxed), 0);
    }

    #[test]
    fn test_tcp_metrics() {
        let metrics = TunMetrics::new();
        
        metrics.record_tcp_connection_start();
        assert_eq!(metrics.tcp_stats.total_connections.load(Ordering::Relaxed), 1);
        assert_eq!(metrics.tcp_stats.active_connections.load(Ordering::Relaxed), 1);

        metrics.record_tcp_connection_end(true, Duration::from_millis(100));
        assert_eq!(metrics.tcp_stats.active_connections.load(Ordering::Relaxed), 0);
        assert_eq!(metrics.tcp_stats.successful_connections.load(Ordering::Relaxed), 1);
    }

    #[test]
    fn test_udp_metrics() {
        let metrics = TunMetrics::new();
        
        metrics.record_udp_packet(1024, true, false);
        assert_eq!(metrics.udp_stats.total_packets.load(Ordering::Relaxed), 1);
        assert_eq!(metrics.udp_stats.processed_packets.load(Ordering::Relaxed), 1);
        assert_eq!(metrics.udp_stats.total_bytes.load(Ordering::Relaxed), 1024);
        assert_eq!(metrics.udp_stats.avg_packet_size.load(Ordering::Relaxed), 1024);
    }

    #[test]
    fn test_error_recording() {
        let metrics = TunMetrics::new();
        
        metrics.record_error("tcp_connection");
        assert_eq!(metrics.error_stats.tcp_connection_errors.load(Ordering::Relaxed), 1);
        
        metrics.record_error("unknown_error");
        // 未知错误不应该增加任何计数器
    }
}
