//! # TUN 模块
//!
//! 本模块提供 TUN 设备的完整功能实现，包括：
//! - TUN 设备管理
//! - 网络栈管理
//! - TCP/UDP 数据处理
//! - 配置管理
//! - 错误处理
//!
//! ## 模块结构
//!
//! - `tun`: 主要的 TUN 设备实现
//! - `config`: 配置管理
//! - `error`: 错误处理
//! - `netstack_manager`: 网络栈管理
//! - `tcp_handler`: TCP 连接处理
//! - `udp_handler`: UDP 数据包处理

pub mod tun;
pub mod config;
pub mod error;
pub mod netstack_manager;
pub mod tcp_handler;
pub mod udp_handler;
pub mod metrics;

#[cfg(test)]
mod tests;

// 重新导出主要类型，方便外部使用
pub use tun::Tun;
pub use config::{TunConfig, BLOCKED_DNS_ADDRESSES};
pub use error::{TunError, TunResult};
pub use netstack_manager::{NetStackManager, NetStackStats};
pub use tcp_handler::{TcpHandler, TcpStats};
pub use udp_handler::{UdpHandler, UdpStats};
pub use metrics::{TunMetrics, TcpMetrics, UdpMetrics, NetStackMetrics, ErrorMetrics, PerformanceMetrics};
