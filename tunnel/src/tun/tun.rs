use std::sync::Arc;

use log::{debug, error, info};
use tokio::sync::mpsc::{channel, Receiver, Sender};
use tokio::sync::RwLock;

use crate::context::context::TunnelContext;

use super::config::TunConfig;
use super::error::{TunError, TunResult};
use super::netstack_manager::NetStackManager;
use super::tcp_handler::TcpHandler;
use super::udp_handler::UdpHandler;
use super::metrics::TunMetrics;



/// TUN 设备结构体
///
/// 重构后的 TUN 设备实现，采用模块化设计，职责分离。
/// 主要负责协调各个组件的工作，而不是直接处理所有逻辑。
/// 集成了性能监控和错误处理机制。
pub struct Tun {
    /// 隧道上下文，包含隧道相关的配置和状态
    tunnel_context: Arc<TunnelContext>,
    /// 从客户端接收数据的通道接收端
    client_receiver: RwLock<Receiver<Vec<u8>>>,
    /// 向 TUN 设备发送数据的通道发送端
    tun_sender: Sender<Vec<u8>>,
    /// TUN 配置
    config: TunConfig,
    /// 网络栈管理器
    netstack_manager: NetStackManager,
    /// TCP 处理器
    tcp_handler: TcpHandler,
    /// UDP 处理器
    udp_handler: UdpHandler,
    /// 性能监控指标
    metrics: Arc<TunMetrics>,
}

impl Tun {
    /// 创建新的 TUN 实例
    ///
    /// # 参数
    /// * `tunnel_context` - 隧道上下文，包含隧道相关的配置和状态
    ///
    /// # 返回值
    /// 返回一个新的 TUN 实例，如果创建失败则返回错误
    pub async fn new(tunnel_context: Arc<TunnelContext>) -> TunResult<Self> {
        Self::with_config(tunnel_context, TunConfig::default()).await
    }

    /// 使用指定配置创建新的 TUN 实例
    ///
    /// # 参数
    /// * `tunnel_context` - 隧道上下文
    /// * `config` - TUN 配置
    ///
    /// # 返回值
    /// 返回配置好的 TUN 实例，如果创建失败则返回错误
    pub async fn with_config(tunnel_context: Arc<TunnelContext>, config: TunConfig) -> TunResult<Self> {
        info!("创建 TUN 实例，配置: {:?}", config);

        // 创建性能监控指标
        let metrics = TunMetrics::new();

        // 验证配置
        let netstack_manager = NetStackManager::new(config.clone());
        if let Err(e) = netstack_manager.validate_config() {
            metrics.record_error("configuration");
            return Err(e);
        }

        // 创建客户端数据通道
        let (client_sender, client_receiver) = channel::<Vec<u8>>(config.channel_buffer_size);
        // 创建 TUN 设备数据通道
        let (tun_sender, tun_receiver) = channel::<Vec<u8>>(config.channel_buffer_size);

        // 创建处理器
        let tcp_handler = TcpHandler::new(tunnel_context.clone(), config.clone());
        let udp_handler = UdpHandler::new(tunnel_context.clone(), config.clone());

        let tun = Tun {
            tunnel_context,
            client_receiver: RwLock::new(client_receiver),
            tun_sender,
            config: config.clone(),
            netstack_manager,
            tcp_handler,
            udp_handler,
            metrics: metrics.clone(),
        };

        // 启动性能监控
        TunMetrics::start_collection(metrics.clone()).await;

        // 启动 TUN 数据处理器
        if let Err(e) = tun.start_data_processing(client_sender, tun_receiver).await {
            metrics.record_error("netstack_init");
            return Err(e);
        }

        info!("TUN 实例创建成功");
        Ok(tun)
    }

    /// 启动数据处理任务
    ///
    /// 这是重构后的核心方法，将原来的复杂逻辑分解为更小的、职责单一的组件。
    ///
    /// # 参数
    /// * `client_sender` - 向客户端发送数据的通道发送端
    /// * `tun_receiver` - 从 TUN 设备接收数据的通道接收端
    ///
    /// # 返回值
    /// 如果启动成功则返回 `Ok(())`，否则返回错误
    async fn start_data_processing(
        &self,
        client_sender: Sender<Vec<u8>>,
        tun_receiver: Receiver<Vec<u8>>,
    ) -> TunResult<()> {
        debug!("启动 TUN 数据处理任务");

        // 创建网络栈实例
        let netstack = self.netstack_manager.create_netstack()?;

        // 启动网络栈数据处理任务
        let (tcp_listener, udp_socket) = self
            .netstack_manager
            .start_data_processing(netstack, client_sender, tun_receiver)
            .await?;

        // 启动 TCP 处理任务
        self.tcp_handler.start_handling(tcp_listener).await?;

        // 启动 UDP 处理任务
        self.udp_handler.start_handling(udp_socket).await?;

        debug!("所有数据处理任务启动完成");
        Ok(())
    }



    /// 获取需要发送到 TUN 网卡的数据
    ///
    /// 从客户端接收通道获取数据，如果通道已关闭则返回空向量
    ///
    /// # 返回值
    /// 返回从客户端接收到的数据包，如果没有数据则返回空向量
    pub async fn get_tun_data(&self) -> Vec<u8> {
        if let Some(data) = self.client_receiver.write().await.recv().await {
            data
        } else {
            vec![]
        }
    }

    /// 处理 TUN 数据包
    ///
    /// 将数据发送到 TUN 设备处理
    ///
    /// # 参数
    /// * `data` - 要处理的数据包
    ///
    /// # 返回值
    /// 如果发送成功则返回 `Ok(())`，否则返回错误
    pub async fn handle_tun_data(&self, data: Vec<u8>) -> TunResult<()> {
        self.tun_sender
            .send(data)
            .await
            .map_err(|e| TunError::ChannelError(format!("TUN数据发送失败: {}", e)))?;
        Ok(())
    }

    /// 获取 TUN 配置
    ///
    /// # 返回值
    /// 返回当前的 TUN 配置
    pub fn config(&self) -> &TunConfig {
        &self.config
    }

    /// 获取网络栈统计信息
    ///
    /// # 返回值
    /// 返回网络栈的统计信息
    pub fn get_netstack_stats(&self) -> super::netstack_manager::NetStackStats {
        self.netstack_manager.get_stats()
    }

    /// 检查地址是否被封锁
    ///
    /// # 参数
    /// * `address` - 要检查的地址
    ///
    /// # 返回值
    /// 如果地址被封锁则返回 `true`，否则返回 `false`
    pub fn is_address_blocked(&self, address: &str) -> bool {
        self.config.is_dns_address_blocked(address)
    }

    /// 更新配置
    ///
    /// # 参数
    /// * `config` - 新的配置
    ///
    /// # 返回值
    /// 如果更新成功则返回 `Ok(())`，否则返回错误
    pub fn update_config(&mut self, config: TunConfig) -> TunResult<()> {
        // 验证新配置
        let temp_manager = NetStackManager::new(config.clone());
        if let Err(e) = temp_manager.validate_config() {
            self.metrics.record_error("configuration");
            return Err(e);
        }

        // 更新配置
        self.config = config.clone();
        self.netstack_manager.update_config(config.clone());
        self.tcp_handler.update_config(config.clone());
        self.udp_handler.update_config(config);

        info!("TUN 配置更新成功");
        Ok(())
    }

    /// 获取性能监控指标
    ///
    /// # 返回值
    /// 返回性能监控指标的引用
    pub fn metrics(&self) -> &Arc<TunMetrics> {
        &self.metrics
    }

    /// 获取统计摘要
    ///
    /// # 返回值
    /// 返回包含主要统计信息的字符串
    pub fn get_stats_summary(&self) -> String {
        self.metrics.get_summary()
    }

    /// 记录错误
    ///
    /// # 参数
    /// * `error_type` - 错误类型
    pub fn record_error(&self, error_type: &str) {
        self.metrics.record_error(error_type);
    }

    /// 健康检查
    ///
    /// # 返回值
    /// 如果系统健康则返回 `true`，否则返回 `false`
    pub fn health_check(&self) -> bool {
        // 检查各种健康指标
        let tcp_total = self.metrics.tcp_stats.total_connections.load(std::sync::atomic::Ordering::Relaxed);
        let tcp_failed = self.metrics.tcp_stats.failed_connections.load(std::sync::atomic::Ordering::Relaxed);
        let udp_total = self.metrics.udp_stats.total_packets.load(std::sync::atomic::Ordering::Relaxed);
        let udp_failed = self.metrics.udp_stats.failed_packets.load(std::sync::atomic::Ordering::Relaxed);

        // 如果失败率超过 50%，认为不健康
        let tcp_healthy = tcp_total == 0 || (tcp_failed * 2 <= tcp_total);
        let udp_healthy = udp_total == 0 || (udp_failed * 2 <= udp_total);

        tcp_healthy && udp_healthy
    }
}
