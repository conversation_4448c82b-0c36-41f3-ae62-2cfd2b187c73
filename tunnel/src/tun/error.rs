//! # TUN 错误处理模块
//!
//! 本模块定义了 TUN 设备相关的错误类型和错误处理机制，
//! 提供统一的错误处理接口和错误恢复策略。

use std::fmt;
use std::io;

/// TUN 操作结果类型别名
pub type TunResult<T> = Result<T, TunError>;

/// TUN 错误类型枚举
///
/// 定义了 TUN 设备操作中可能出现的各种错误类型
#[derive(Debug)]
pub enum TunError {
    /// 网络栈初始化错误
    ///
    /// 当网络栈创建或初始化失败时返回此错误
    NetStackInitError(String),

    /// 通道操作错误
    ///
    /// 当异步通道发送或接收操作失败时返回此错误
    ChannelError(String),

    /// TCP 连接错误
    ///
    /// 当 TCP 连接建立或数据传输失败时返回此错误
    TcpConnectionError(String),

    /// UDP 数据传输错误
    ///
    /// 当 UDP 数据包发送或接收失败时返回此错误
    UdpTransmissionError(String),

    /// 隧道连接错误
    ///
    /// 当隧道连接建立或数据传输失败时返回此错误
    TunnelConnectionError(String),

    /// DNS 解析错误
    ///
    /// 当 DNS 查询或解析失败时返回此错误
    DnsResolutionError(String),

    /// 配置错误
    ///
    /// 当配置参数无效或配置加载失败时返回此错误
    ConfigurationError(String),

    /// I/O 错误
    ///
    /// 当底层 I/O 操作失败时返回此错误
    IoError(io::Error),

    /// 网络地址解析错误
    ///
    /// 当网络地址格式无效或解析失败时返回此错误
    AddressParseError(String),

    /// 超时错误
    ///
    /// 当操作超时时返回此错误
    TimeoutError(String),

    /// 资源不足错误
    ///
    /// 当系统资源不足时返回此错误
    ResourceExhaustedError(String),

    /// 未知错误
    ///
    /// 当遇到未预期的错误时返回此错误
    UnknownError(String),
}

impl fmt::Display for TunError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            TunError::NetStackInitError(msg) => {
                write!(f, "网络栈初始化错误: {}", msg)
            }
            TunError::ChannelError(msg) => {
                write!(f, "通道操作错误: {}", msg)
            }
            TunError::TcpConnectionError(msg) => {
                write!(f, "TCP 连接错误: {}", msg)
            }
            TunError::UdpTransmissionError(msg) => {
                write!(f, "UDP 数据传输错误: {}", msg)
            }
            TunError::TunnelConnectionError(msg) => {
                write!(f, "隧道连接错误: {}", msg)
            }
            TunError::DnsResolutionError(msg) => {
                write!(f, "DNS 解析错误: {}", msg)
            }
            TunError::ConfigurationError(msg) => {
                write!(f, "配置错误: {}", msg)
            }
            TunError::IoError(err) => {
                write!(f, "I/O 错误: {}", err)
            }
            TunError::AddressParseError(msg) => {
                write!(f, "网络地址解析错误: {}", msg)
            }
            TunError::TimeoutError(msg) => {
                write!(f, "超时错误: {}", msg)
            }
            TunError::ResourceExhaustedError(msg) => {
                write!(f, "资源不足错误: {}", msg)
            }
            TunError::UnknownError(msg) => {
                write!(f, "未知错误: {}", msg)
            }
        }
    }
}

impl std::error::Error for TunError {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        match self {
            TunError::IoError(err) => Some(err),
            _ => None,
        }
    }
}

impl From<io::Error> for TunError {
    fn from(err: io::Error) -> Self {
        TunError::IoError(err)
    }
}

impl From<tokio::sync::mpsc::error::SendError<Vec<u8>>> for TunError {
    fn from(err: tokio::sync::mpsc::error::SendError<Vec<u8>>) -> Self {
        TunError::ChannelError(format!("通道发送错误: {}", err))
    }
}

impl From<std::net::AddrParseError> for TunError {
    fn from(err: std::net::AddrParseError) -> Self {
        TunError::AddressParseError(format!("地址解析错误: {}", err))
    }
}

/// 错误恢复策略枚举
///
/// 定义了不同错误类型的恢复策略
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum RecoveryStrategy {
    /// 立即重试
    RetryImmediately,
    /// 延迟重试
    RetryWithDelay,
    /// 跳过当前操作
    Skip,
    /// 终止操作
    Abort,
    /// 降级处理
    Fallback,
}

impl TunError {
    /// 获取错误的恢复策略
    ///
    /// # 返回值
    /// 返回适合当前错误类型的恢复策略
    pub fn recovery_strategy(&self) -> RecoveryStrategy {
        match self {
            TunError::NetStackInitError(_) => RecoveryStrategy::Abort,
            TunError::ChannelError(_) => RecoveryStrategy::RetryWithDelay,
            TunError::TcpConnectionError(_) => RecoveryStrategy::RetryWithDelay,
            TunError::UdpTransmissionError(_) => RecoveryStrategy::Skip,
            TunError::TunnelConnectionError(_) => RecoveryStrategy::RetryWithDelay,
            TunError::DnsResolutionError(_) => RecoveryStrategy::Fallback,
            TunError::ConfigurationError(_) => RecoveryStrategy::Abort,
            TunError::IoError(_) => RecoveryStrategy::RetryWithDelay,
            TunError::AddressParseError(_) => RecoveryStrategy::Skip,
            TunError::TimeoutError(_) => RecoveryStrategy::RetryImmediately,
            TunError::ResourceExhaustedError(_) => RecoveryStrategy::RetryWithDelay,
            TunError::UnknownError(_) => RecoveryStrategy::Skip,
        }
    }

    /// 判断错误是否可以重试
    ///
    /// # 返回值
    /// 如果错误可以重试则返回 `true`，否则返回 `false`
    pub fn is_retryable(&self) -> bool {
        matches!(
            self.recovery_strategy(),
            RecoveryStrategy::RetryImmediately | RecoveryStrategy::RetryWithDelay
        )
    }

    /// 判断错误是否是致命的
    ///
    /// # 返回值
    /// 如果错误是致命的则返回 `true`，否则返回 `false`
    pub fn is_fatal(&self) -> bool {
        matches!(self.recovery_strategy(), RecoveryStrategy::Abort)
    }

    /// 获取建议的重试延迟时间（毫秒）
    ///
    /// # 返回值
    /// 返回建议的重试延迟时间，如果不需要延迟则返回 0
    pub fn retry_delay_ms(&self) -> u64 {
        match self {
            TunError::ChannelError(_) => 100,
            TunError::TcpConnectionError(_) => 1000,
            TunError::TunnelConnectionError(_) => 2000,
            TunError::IoError(_) => 500,
            TunError::ResourceExhaustedError(_) => 5000,
            _ => 0,
        }
    }
}

/// 错误处理工具函数
pub mod error_utils {
    use super::*;
    use log::{error, warn, debug};
    use tokio::time::{sleep, Duration};

    /// 记录错误日志
    ///
    /// # 参数
    /// * `error` - 要记录的错误
    /// * `context` - 错误上下文信息
    pub fn log_error(error: &TunError, context: &str) {
        match error.recovery_strategy() {
            RecoveryStrategy::Abort => {
                error!("[{}] 致命错误: {}", context, error);
            }
            RecoveryStrategy::RetryWithDelay | RecoveryStrategy::RetryImmediately => {
                warn!("[{}] 可重试错误: {}", context, error);
            }
            _ => {
                debug!("[{}] 非致命错误: {}", context, error);
            }
        }
    }

    /// 执行错误恢复策略
    ///
    /// # 参数
    /// * `error` - 要处理的错误
    /// * `context` - 错误上下文信息
    ///
    /// # 返回值
    /// 返回是否应该继续执行后续操作
    pub async fn handle_error_recovery(error: &TunError, context: &str) -> bool {
        log_error(error, context);

        match error.recovery_strategy() {
            RecoveryStrategy::RetryWithDelay => {
                let delay = error.retry_delay_ms();
                if delay > 0 {
                    debug!("[{}] 延迟 {}ms 后重试", context, delay);
                    sleep(Duration::from_millis(delay)).await;
                }
                true
            }
            RecoveryStrategy::RetryImmediately => true,
            RecoveryStrategy::Skip => {
                debug!("[{}] 跳过当前操作", context);
                true
            }
            RecoveryStrategy::Fallback => {
                debug!("[{}] 使用降级处理", context);
                true
            }
            RecoveryStrategy::Abort => {
                error!("[{}] 终止操作", context);
                false
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_display() {
        let error = TunError::NetStackInitError("test error".to_string());
        assert_eq!(format!("{}", error), "网络栈初始化错误: test error");
    }

    #[test]
    fn test_error_recovery_strategy() {
        assert_eq!(
            TunError::NetStackInitError("test".to_string()).recovery_strategy(),
            RecoveryStrategy::Abort
        );
        assert_eq!(
            TunError::ChannelError("test".to_string()).recovery_strategy(),
            RecoveryStrategy::RetryWithDelay
        );
        assert_eq!(
            TunError::UdpTransmissionError("test".to_string()).recovery_strategy(),
            RecoveryStrategy::Skip
        );
    }

    #[test]
    fn test_error_properties() {
        let fatal_error = TunError::NetStackInitError("test".to_string());
        assert!(fatal_error.is_fatal());
        assert!(!fatal_error.is_retryable());

        let retryable_error = TunError::ChannelError("test".to_string());
        assert!(!retryable_error.is_fatal());
        assert!(retryable_error.is_retryable());
        assert!(retryable_error.retry_delay_ms() > 0);
    }

    #[test]
    fn test_error_conversion() {
        let io_error = io::Error::new(io::ErrorKind::ConnectionRefused, "connection refused");
        let tun_error: TunError = io_error.into();
        
        match tun_error {
            TunError::IoError(_) => (),
            _ => panic!("Expected IoError"),
        }
    }
}
