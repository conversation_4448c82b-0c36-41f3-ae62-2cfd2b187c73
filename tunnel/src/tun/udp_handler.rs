//! # UDP 处理器模块
//!
//! 本模块负责处理 TUN 设备的 UDP 数据包，包括数据包转发、
//! DNS 封锁检查、隧道映射信息创建等功能。

use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use futures::{sink::SinkExt, stream::StreamExt};

use log::{debug, error, warn};
use netstack_lwip::{UdpSocket, SendHalf};
use tokio::spawn;

use flyshadow_common::tunnel::tunnel_package::PackageProtocol;

use crate::context::context::TunnelContext;
use crate::context::proxy_type::ProxyType;
use crate::mapper::tunnel_mapper_info::TunnelMapperInfo;
use crate::util::process_util::get_process_name_by_port;

use super::config::TunConfig;
use super::error::{error_utils, TunError, TunResult};

/// UDP 数据包处理器
///
/// 负责处理来自网络栈的 UDP 数据包
pub struct UdpHandler {
    /// 隧道上下文
    tunnel_context: Arc<TunnelContext>,
    /// TUN 配置
    config: TunConfig,
}

/// UDP 数据包统计信息
#[derive(Debug, Default, Clone)]
pub struct UdpStats {
    /// 总数据包数
    pub total_packets: u64,
    /// 处理的数据包数
    pub processed_packets: u64,
    /// 被封锁的数据包数
    pub blocked_packets: u64,
    /// 处理失败的数据包数
    pub failed_packets: u64,
    /// 总字节数
    pub total_bytes: u64,
}

impl UdpHandler {
    /// 创建新的 UDP 处理器
    ///
    /// # 参数
    /// * `tunnel_context` - 隧道上下文
    /// * `config` - TUN 配置
    ///
    /// # 返回值
    /// 返回新的 UDP 处理器实例
    pub fn new(tunnel_context: Arc<TunnelContext>, config: TunConfig) -> Self {
        Self {
            tunnel_context,
            config,
        }
    }

    /// 启动 UDP 数据包处理任务
    ///
    /// # 参数
    /// * `udp_socket` - UDP 套接字
    ///
    /// # 返回值
    /// 如果启动成功则返回 `Ok(())`，否则返回错误
    pub async fn start_handling(&self, udp_socket: UdpSocket) -> TunResult<()> {
        debug!("启动 UDP 数据包处理任务");

        let tunnel_context = self.tunnel_context.clone();
        let config = self.config.clone();

        spawn(async move {
            let mut stats = UdpStats::default();
            
            // 分离 UDP socket 的读写部分
            let (writer, mut reader) = udp_socket.split();
            let writer = Arc::new(writer);

            while let Ok((udp_msg, src_addr, dst_addr)) = reader.recv_from().await {
                stats.total_packets += 1;
                stats.total_bytes += udp_msg.len() as u64;

                // 检查是否为需要封锁的 DNS 加密地址
                let dst_addr_str = dst_addr.to_string();
                if config.is_dns_address_blocked(&dst_addr_str) {
                    debug!("封锁 DNS 加密 UDP 数据包: {}", dst_addr_str);
                    stats.blocked_packets += 1;
                    continue;
                }

                stats.processed_packets += 1;

                // 处理 UDP 数据包
                if let Err(e) = Self::handle_udp_packet(
                    tunnel_context.clone(),
                    writer.clone(),
                    &udp_msg,
                    src_addr,
                    dst_addr,
                ).await {
                    error_utils::log_error(&e, "UDP数据包处理");
                    stats.failed_packets += 1;
                }

                // 定期输出统计信息
                if stats.total_packets % 1000 == 0 {
                    debug!("UDP 数据包统计: {}", stats);
                }
            }

            warn!("UDP 套接字已关闭");
        });

        debug!("UDP 数据包处理任务启动完成");
        Ok(())
    }

    /// 处理单个 UDP 数据包
    ///
    /// # 参数
    /// * `tunnel_context` - 隧道上下文
    /// * `writer` - UDP 发送端
    /// * `data` - 数据包内容
    /// * `src_addr` - 源地址
    /// * `dst_addr` - 目标地址
    ///
    /// # 返回值
    /// 如果处理成功则返回 `Ok(())`，否则返回错误
    async fn handle_udp_packet(
        tunnel_context: Arc<TunnelContext>,
        writer: Arc<SendHalf>,
        data: &[u8],
        src_addr: SocketAddr,
        dst_addr: SocketAddr,
    ) -> TunResult<()> {
        debug!("处理 UDP 数据包 {} -> {}, 大小: {} 字节", src_addr, dst_addr, data.len());

        // 创建隧道映射信息
        let tunnel_mapper_info = Self::create_tunnel_mapper_info(
            src_addr.ip().to_string(),
            src_addr.port(),
            dst_addr.ip().to_string(),
            dst_addr.port(),
        );

        // 发送 UDP 数据到服务端
        tunnel_context
            .tun_udp_write_to_server(tunnel_mapper_info, writer, data)
            .await;

        debug!("UDP 数据包处理完成 {} -> {}", src_addr, dst_addr);
        Ok(())
    }

    /// 创建隧道映射信息
    ///
    /// # 参数
    /// * `source_addr` - 源地址
    /// * `source_port` - 源端口
    /// * `target_addr` - 目标地址
    /// * `target_port` - 目标端口
    ///
    /// # 返回值
    /// 返回配置好的 TunnelMapperInfo 实例
    fn create_tunnel_mapper_info(
        source_addr: String,
        source_port: u16,
        target_addr: String,
        target_port: u16,
    ) -> TunnelMapperInfo {
        TunnelMapperInfo {
            protocol: PackageProtocol::UDP,
            source_addr,
            source_port,
            target_addr,
            target_port,
            fake_target_addr: String::new(),
            fake_target_port: 0,
            process_name: get_process_name_by_port(source_port, PackageProtocol::UDP),
            matcher_name: String::new(),
            matcher_rule: String::new(),
            proxy_type: ProxyType::Proxy,
            active_time: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis(),
            temp_data: Vec::new(),
            direct_conn_priority: true,
            traffic_info: Arc::new(Default::default()),
        }
    }

    /// 获取配置信息
    ///
    /// # 返回值
    /// 返回当前的 TUN 配置
    pub fn config(&self) -> &TunConfig {
        &self.config
    }

    /// 更新配置
    ///
    /// # 参数
    /// * `config` - 新的配置
    pub fn update_config(&mut self, config: TunConfig) {
        debug!("更新 UDP 处理器配置");
        self.config = config;
    }

    /// 检查地址是否被封锁
    ///
    /// # 参数
    /// * `address` - 要检查的地址
    ///
    /// # 返回值
    /// 如果地址被封锁则返回 `true`，否则返回 `false`
    pub fn is_address_blocked(&self, address: &str) -> bool {
        self.config.is_dns_address_blocked(address)
    }
}

impl std::fmt::Display for UdpStats {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "UDP Stats - Total: {}, Processed: {}, Blocked: {}, Failed: {}, Bytes: {}",
            self.total_packets, self.processed_packets, self.blocked_packets, 
            self.failed_packets, self.total_bytes
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_tunnel_mapper_info() {
        let info = UdpHandler::create_tunnel_mapper_info(
            "***********".to_string(),
            8080,
            "*******".to_string(),
            53,
        );

        assert_eq!(info.protocol, PackageProtocol::UDP);
        assert_eq!(info.source_addr, "***********");
        assert_eq!(info.source_port, 8080);
        assert_eq!(info.target_addr, "*******");
        assert_eq!(info.target_port, 53);
        assert_eq!(info.proxy_type, ProxyType::Proxy);
        assert!(info.direct_conn_priority);
    }

    #[test]
    fn test_udp_stats_display() {
        let stats = UdpStats {
            total_packets: 1000,
            processed_packets: 950,
            blocked_packets: 30,
            failed_packets: 20,
            total_bytes: 50000,
        };

        let display = format!("{}", stats);
        assert!(display.contains("Total: 1000"));
        assert!(display.contains("Processed: 950"));
        assert!(display.contains("Blocked: 30"));
        assert!(display.contains("Failed: 20"));
        assert!(display.contains("Bytes: 50000"));
    }
}
